#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
MobileNetV2TSM配置验证脚本
验证out_indices与img_feat_dim的一致性
"""

import os
import sys
import glob
import re

def validate_mobilenetv2_config(config_path):
    """验证单个配置文件的MobileNetV2TSM设置"""
    print(f"\n🔍 验证配置文件: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用MobileNetV2TSM
        if 'MobileNetV2TSM' not in content:
            print("   ⏭️  跳过：不使用MobileNetV2TSM")
            return True
        
        # 提取out_indices设置
        out_indices_match = re.search(r'out_indices=\((\d+),?\)', content)
        if not out_indices_match:
            print("   ❌ 未找到out_indices设置")
            return False
        
        out_index = int(out_indices_match.group(1))
        
        # 提取img_feat_dim设置
        img_feat_dim_match = re.search(r'img_feat_dim=(\d+)', content)
        if not img_feat_dim_match:
            print("   ❌ 未找到img_feat_dim设置")
            return False
        
        img_feat_dim = int(img_feat_dim_match.group(1))
        
        # 验证一致性
        expected_dims = {
            0: 16, 1: 24, 2: 32, 3: 64, 4: 96, 5: 160, 6: 320, 7: 1280
        }
        
        expected_dim = expected_dims.get(out_index)
        if expected_dim is None:
            print(f"   ❌ 无效的out_indices值: {out_index}")
            return False
        
        if img_feat_dim == expected_dim:
            print(f"   ✅ 配置正确: out_indices=({out_index},) → img_feat_dim={img_feat_dim}")
            return True
        else:
            print(f"   ❌ 配置不匹配: out_indices=({out_index},) 应对应 img_feat_dim={expected_dim}, 但实际为 {img_feat_dim}")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 MobileNetV2TSM配置一致性验证")
    print("=" * 80)
    
    # 查找所有多模态配置文件
    config_pattern = "configs/recognition/Multimodal/*.py"
    config_files = glob.glob(config_pattern)
    
    if not config_files:
        print(f"❌ 未找到配置文件: {config_pattern}")
        return False
    
    print(f"📁 找到 {len(config_files)} 个配置文件")
    
    # 验证每个配置文件
    success_count = 0
    total_count = 0
    
    for config_file in sorted(config_files):
        total_count += 1
        if validate_mobilenetv2_config(config_file):
            success_count += 1
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 验证结果总结")
    print("=" * 80)
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有配置文件验证通过！")
        return True
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个配置文件需要修复")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
